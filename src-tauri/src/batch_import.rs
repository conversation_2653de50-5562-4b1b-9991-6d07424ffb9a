#![allow(non_snake_case)]

use reqwest;
use scraper::{Html, Selector};
use serde::{Deserialize, Serialize};
use std::collections::{HashSet, VecDeque};
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::time::sleep;
use url::Url;

use crate::recipe_import::{import_recipe_from_url, ImportedRecipe, RecipeImportError};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchImportRequest {
    pub start_url: String,
    pub max_recipes: Option<u32>,
    pub max_depth: Option<u32>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BatchImportProgress {
    pub status: BatchImportStatus,
    pub current_url: Option<String>,
    pub processed_recipes: u32,
    pub total_recipes: u32,
    pub processed_categories: u32,
    pub total_categories: u32,
    pub successful_imports: u32,
    pub failed_imports: u32,
    pub errors: Vec<BatchImportError>,
    pub start_time: String,
    pub estimated_time_remaining: Option<u32>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BatchImportResult {
    pub success: bool,
    pub total_processed: u32,
    pub successful_imports: u32,
    pub failed_imports: u32,
    pub errors: Vec<BatchImportError>,
    pub imported_recipe_ids: Vec<String>,
    pub duration: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchImportError {
    pub url: String,
    pub message: String,
    pub timestamp: String,
    pub error_type: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BatchImportStatus {
    Idle,
    Starting,
    CrawlingCategories,
    ExtractingRecipes,
    ImportingRecipes,
    Completed,
    Cancelled,
    Error,
}

#[derive(Debug, Clone)]
pub struct CategoryInfo {
    pub name: String,
    pub url: String,
    pub processed: bool,
    pub recipe_count: Option<u32>,
}

#[derive(Debug, Clone)]
pub struct RecipeUrlInfo {
    pub url: String,
    pub processed: bool,
    pub imported: bool,
    pub error: Option<String>,
}

pub struct BatchImporter {
    progress: Arc<Mutex<BatchImportProgress>>,
    client: reqwest::Client,
    cancelled: Arc<Mutex<bool>>,
}

impl BatchImporter {
    pub fn new() -> Self {
        let client = reqwest::Client::builder()
            .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            .timeout(Duration::from_secs(30))
            .build()
            .unwrap();

        let progress = BatchImportProgress {
            status: BatchImportStatus::Idle,
            current_url: None,
            processed_recipes: 0,
            total_recipes: 0,
            processed_categories: 0,
            total_categories: 0,
            successful_imports: 0,
            failed_imports: 0,
            errors: Vec::new(),
            start_time: chrono::Utc::now().to_rfc3339(),
            estimated_time_remaining: None,
        };

        Self {
            progress: Arc::new(Mutex::new(progress)),
            client,
            cancelled: Arc::new(Mutex::new(false)),
        }
    }

    pub fn get_progress(&self) -> BatchImportProgress {
        self.progress.lock().unwrap().clone()
    }

    pub fn cancel(&self) {
        *self.cancelled.lock().unwrap() = true;
        let mut progress = self.progress.lock().unwrap();
        progress.status = BatchImportStatus::Cancelled;
    }

    fn is_cancelled(&self) -> bool {
        *self.cancelled.lock().unwrap()
    }

    fn update_status(&self, status: BatchImportStatus) {
        let mut progress = self.progress.lock().unwrap();
        progress.status = status;
    }

    fn add_error(&self, url: String, message: String, error_type: String) {
        let mut progress = self.progress.lock().unwrap();
        progress.errors.push(BatchImportError {
            url,
            message,
            timestamp: chrono::Utc::now().to_rfc3339(),
            error_type,
        });
    }

    pub async fn start_batch_import(&self, request: BatchImportRequest) -> Result<BatchImportResult, String> {
        let start_time = Instant::now();
        
        // Reset state
        *self.cancelled.lock().unwrap() = false;
        {
            let mut progress = self.progress.lock().unwrap();
            progress.status = BatchImportStatus::Starting;
            progress.start_time = chrono::Utc::now().to_rfc3339();
            progress.errors.clear();
            progress.processed_recipes = 0;
            progress.successful_imports = 0;
            progress.failed_imports = 0;
        }

        // Validate start URL
        let start_url = match Url::parse(&request.start_url) {
            Ok(url) => url,
            Err(_) => return Err("Invalid start URL".to_string()),
        };

        if !start_url.host_str().unwrap_or("").contains("allrecipes.com") {
            return Err("Only AllRecipes.com URLs are supported for batch import".to_string());
        }

        // Step 1: Crawl categories
        self.update_status(BatchImportStatus::CrawlingCategories);
        let categories = match self.crawl_categories(&request.start_url).await {
            Ok(cats) => cats,
            Err(e) => {
                self.update_status(BatchImportStatus::Error);
                return Err(format!("Failed to crawl categories: {}", e));
            }
        };

        if self.is_cancelled() {
            return Ok(self.build_result(start_time));
        }

        // Step 2: Extract recipe URLs from all categories
        self.update_status(BatchImportStatus::ExtractingRecipes);
        let recipe_urls = match self.extract_all_recipe_urls(&categories).await {
            Ok(urls) => urls,
            Err(e) => {
                self.update_status(BatchImportStatus::Error);
                return Err(format!("Failed to extract recipe URLs: {}", e));
            }
        };

        if self.is_cancelled() {
            return Ok(self.build_result(start_time));
        }

        // Apply max_recipes limit if specified
        let limited_urls = if let Some(max) = request.max_recipes {
            recipe_urls.into_iter().take(max as usize).collect()
        } else {
            recipe_urls
        };

        // Update total count
        {
            let mut progress = self.progress.lock().unwrap();
            progress.total_recipes = limited_urls.len() as u32;
        }

        // Step 3: Import recipes
        self.update_status(BatchImportStatus::ImportingRecipes);
        self.import_recipes(limited_urls).await;

        self.update_status(BatchImportStatus::Completed);
        Ok(self.build_result(start_time))
    }

    async fn crawl_categories(&self, start_url: &str) -> Result<Vec<CategoryInfo>, String> {
        let response = self.client.get(start_url).send().await
            .map_err(|e| format!("Failed to fetch start URL: {}", e))?;

        let html = response.text().await
            .map_err(|e| format!("Failed to read response: {}", e))?;

        let document = Html::parse_document(&html);
        let mut categories = Vec::new();

        // Add the starting category itself
        categories.push(CategoryInfo {
            name: "Main Category".to_string(),
            url: start_url.to_string(),
            processed: false,
            recipe_count: None,
        });

        // Extract subcategory links
        // AllRecipes typically has category links in navigation or sidebar
        let category_selectors = [
            "a[href*='/recipes/']",
            ".category-link",
            ".subcategory-link",
            "nav a[href*='/recipes/']",
        ];

        for selector_str in &category_selectors {
            if let Ok(selector) = Selector::parse(selector_str) {
                for element in document.select(&selector) {
                    if let Some(href) = element.value().attr("href") {
                        if let Some(text) = element.text().next() {
                            let full_url = self.resolve_url(start_url, href)?;
                            
                            // Filter for valid category URLs
                            if self.is_valid_category_url(&full_url) {
                                categories.push(CategoryInfo {
                                    name: text.trim().to_string(),
                                    url: full_url,
                                    processed: false,
                                    recipe_count: None,
                                });
                            }
                        }
                    }
                }
            }
        }

        // Remove duplicates
        categories.sort_by(|a, b| a.url.cmp(&b.url));
        categories.dedup_by(|a, b| a.url == b.url);

        {
            let mut progress = self.progress.lock().unwrap();
            progress.total_categories = categories.len() as u32;
        }

        Ok(categories)
    }

    fn resolve_url(&self, base: &str, href: &str) -> Result<String, String> {
        let base_url = Url::parse(base).map_err(|_| "Invalid base URL")?;
        let resolved = base_url.join(href).map_err(|_| "Failed to resolve URL")?;
        Ok(resolved.to_string())
    }

    fn is_valid_category_url(&self, url: &str) -> bool {
        if let Ok(parsed) = Url::parse(url) {
            if let Some(host) = parsed.host_str() {
                return host.contains("allrecipes.com") && 
                       parsed.path().contains("/recipes/") &&
                       !parsed.path().contains("/recipe/"); // Exclude individual recipes
            }
        }
        false
    }

    async fn extract_all_recipe_urls(&self, categories: &[CategoryInfo]) -> Result<Vec<String>, String> {
        let mut all_recipe_urls = HashSet::new();

        for (index, category) in categories.iter().enumerate() {
            if self.is_cancelled() {
                break;
            }

            {
                let mut progress = self.progress.lock().unwrap();
                progress.current_url = Some(category.url.clone());
                progress.processed_categories = index as u32;
            }

            match self.extract_recipe_urls_from_page(&category.url).await {
                Ok(urls) => {
                    for url in urls {
                        all_recipe_urls.insert(url);
                    }
                }
                Err(e) => {
                    self.add_error(
                        category.url.clone(),
                        format!("Failed to extract recipes: {}", e),
                        "ExtractionError".to_string(),
                    );
                }
            }

            // Add delay between requests to be respectful
            sleep(Duration::from_millis(1000)).await;
        }

        Ok(all_recipe_urls.into_iter().collect())
    }

    async fn extract_recipe_urls_from_page(&self, page_url: &str) -> Result<Vec<String>, String> {
        let response = self.client.get(page_url).send().await
            .map_err(|e| format!("Failed to fetch page: {}", e))?;

        let html = response.text().await
            .map_err(|e| format!("Failed to read response: {}", e))?;

        let document = Html::parse_document(&html);
        let mut recipe_urls = Vec::new();

        // AllRecipes recipe link selectors
        let recipe_selectors = [
            "a[href*='/recipe/']",
            ".recipe-card a",
            ".card-recipe a",
            ".recipe-link",
        ];

        for selector_str in &recipe_selectors {
            if let Ok(selector) = Selector::parse(selector_str) {
                for element in document.select(&selector) {
                    if let Some(href) = element.value().attr("href") {
                        let full_url = self.resolve_url(page_url, href)?;

                        if self.is_valid_recipe_url(&full_url) {
                            recipe_urls.push(full_url);
                        }
                    }
                }
            }
        }

        // Remove duplicates
        recipe_urls.sort();
        recipe_urls.dedup();

        Ok(recipe_urls)
    }

    fn is_valid_recipe_url(&self, url: &str) -> bool {
        if let Ok(parsed) = Url::parse(url) {
            if let Some(host) = parsed.host_str() {
                let path = parsed.path();

                // Must be AllRecipes and contain /recipe/
                if !host.contains("allrecipes.com") || !path.contains("/recipe/") {
                    return false;
                }

                // Skip URLs that start with a number (non-recipe content)
                // Only check if there's a meaningful path segment after /recipe/
                let path_segments: Vec<&str> = path.split('/').filter(|s| !s.is_empty()).collect();
                if path_segments.len() >= 3 && path_segments[0] == "recipe" {
                    // Check if the ID segment (second element) is followed by a name
                    if path_segments.len() == 2 {
                        // URL like /recipe/123/ - invalid
                        return false;
                    }
                    // URL like /recipe/123/recipe-name - valid
                    return true;
                } else if path_segments.len() == 2 && path_segments[0] == "recipe" {
                    // URL like /recipe/123 - invalid
                    return false;
                }

                return true;
            }
        }
        false
    }

    async fn import_recipes(&self, recipe_urls: Vec<String>) {
        for (index, url) in recipe_urls.iter().enumerate() {
            if self.is_cancelled() {
                break;
            }

            {
                let mut progress = self.progress.lock().unwrap();
                progress.current_url = Some(url.clone());
                progress.processed_recipes = index as u32;
            }

            match import_recipe_from_url(url).await {
                Ok(_recipe) => {
                    let mut progress = self.progress.lock().unwrap();
                    progress.successful_imports += 1;
                }
                Err(e) => {
                    let mut progress = self.progress.lock().unwrap();
                    progress.failed_imports += 1;

                    self.add_error(
                        url.clone(),
                        e.message.clone(),
                        e.error_type.clone(),
                    );
                }
            }

            // Add delay between imports to be respectful
            sleep(Duration::from_millis(2000)).await;
        }
    }

    fn build_result(&self, start_time: Instant) -> BatchImportResult {
        let progress = self.progress.lock().unwrap();
        BatchImportResult {
            success: matches!(progress.status, BatchImportStatus::Completed),
            total_processed: progress.processed_recipes,
            successful_imports: progress.successful_imports,
            failed_imports: progress.failed_imports,
            errors: progress.errors.clone(),
            imported_recipe_ids: Vec::new(), // TODO: Track actual imported IDs
            duration: start_time.elapsed().as_secs() as u32,
        }
    }
}

#[cfg(test)]
mod tests;
